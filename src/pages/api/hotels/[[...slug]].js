import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]";
import { getCollection } from "@/lib/db/mongodb";
import { ObjectId } from "mongodb";

export default async function handler(req, res) {
  const { slug = [] } = req.query;
  const dtBOP = Date.now();
  // Eğer slug'ın ilk elemanı "stats" ise
  if (slug[0] === "stats") {
    const { w,  } = req.query;
    let query = {};

    // w parametresine göre sorgu oluştur
    if (w === "hotelstatistics") { 
        const pipeline = [
            {
                $group: {
                    _id: null,
                    toplam_tesis: { $sum: 1 },
                    listelenen_tesisler: {
                        $sum: {
                            $cond: [
                                { $eq: ["$WEBSITE_LISTED", "TRUE"] },
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    toplam_tesis: 1,
                    listelenen_tesisler: 1
                }
            }
        ];
      let resp = {};
        const collection = await getCollection("tourai.data.dim.hotelsdomestic");
      try {
        const data = await collection.aggregate(pipeline).toArray();
        resp = {
            ...data[0],
        }
        // return res.status(200).json(data[0]);
      } catch (error) {
        return res.status(500).json({ error: "Database error", details: error.message });
      }

        // const pipeline2 = [
        //     {
        //         $match: {
        //             WEBSITE_LISTED: "TRUE"
        //         }
        //     },
        //     {
        //         $group: {
        //             _id: {
        //                 tesisOnay: "$TESIS_ONAY",
        //                 durum: "$DURUM"
        //             },
        //             count: { $sum: 1 }
        //         }
        //     },
        //     {
        //         $project: {
        //             _id: 0,
        //             TESIS_ONAY: "$_id.tesisOnay",
        //             DURUM: "$_id.durum",
        //             count: 1
        //         }
        //     },
        //     {
        //         $sort: {
        //             TESIS_ONAY: 1,
        //             DURUM: 1
        //         }
        //     }
        // ]
        const pipeline3 = [
            {
                $group: {
                    _id: {
                        tesisOnay: "$TESIS_ONAY",
                        durum: "$DURUM",
                        WEBSITE_LISTED: "$WEBSITE_LISTED",
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $project: {
                    _id: 0,
                    TESIS_ONAY: "$_id.tesisOnay",
                    DURUM: "$_id.durum",
                    listelenen_tesisler: "$_id.WEBSITE_LISTED",
                    count: 1
                }
            },
            {
                $sort: {
                    TESIS_ONAY: 1,
                    DURUM: 1
                }
            }
        ];
        const data2 = await collection.aggregate(pipeline3).toArray();
        resp = {
            ...resp,
            tesis_durumlari: data2,
            elapsed: Date.now() - dtBOP,
        }
      return res.status(200).json({...resp});

    } else if (w === "books") {
        const pipeline = [
            // Dinamik Tarih Hesaplamaları
            {
                $addFields: {
                    currentDate: "$$NOW",
                    currentDay: { $dayOfMonth: "$$NOW" },

                    currentYearMonth: {
                        $dateToString: {
                            format: "%Y-%m",
                            date: "$$NOW"
                        }
                    },

                    previousMonthDate: {
                        $dateSubtract: {
                            startDate: "$$NOW",
                            unit: "month",
                            amount: 1
                        }
                    },

                    lastYearSameMonthDate: {
                        $dateSubtract: {
                            startDate: "$$NOW",
                            unit: "year",
                            amount: 1
                        }
                    }
                }
            },
            {
                $addFields: {
                    previousYearMonth: {
                        $dateToString: {
                            format: "%Y-%m",
                            date: "$previousMonthDate"
                        }
                    },
                    lastYearSameMonth: {
                        $dateToString: {
                            format: "%Y-%m",
                            date: "$lastYearSameMonthDate"
                        }
                    }
                }
            },

            // Filtreleme: cari ay, önceki ay ve geçen yıl aynı ay; ayrıca SATIS_GUN <= bugünkü gün
            {
                $match: {
                    $expr: {
                        $and: [
                            {
                                $in: ["$SATIS_DONEM", ["$currentYearMonth", "$previousYearMonth", "$lastYearSameMonth"]]
                            },
                            { $lte: ["$SATIS_GUN", "$currentDay"] }
                        ]
                    }
                }
            },

            // Her dönem için metrikleri grupla ve ilgili dinamik tarih alanlarını da sakla
            // BURADAKİ DEĞİŞİKLİK: Dinamik tarih alanlarını `$first` ile group'tan geçiriyoruz
            {
                $group: {
                    _id: "$SATIS_DONEM",
                    distinctTesisSayisi: { $addToSet: "$TESIS_ID" },
                    toplamRezvSayisi: { $sum: "$REZV" },
                    toplamRezvGunSayisi: { $sum: "$REZV_GUNSAYISI" },
                    toplamCiro: { $sum: "$tTutar" },
                    // Dinamik tarih alanlarını da `$first` ile alıyoruz ki sonraki aşamalarda kullanabilelim
                    currentYearMonth: { $first: "$currentYearMonth" },
                    previousYearMonth: { $first: "$previousYearMonth" },
                    lastYearSameMonth: { $first: "$lastYearSameMonth" }
                }
            },

            // Sonuçları yeniden düzenle ve isimlerini ver
            {
                $project: {
                    _id: 0,
                    SATIS_DONEM: "$_id",
                    distinctTesisSayisi: { $size: "$distinctTesisSayisi" },
                    toplamRezvSayisi: 1,
                    toplamRezvGunSayisi: 1,
                    toplamCiro: 1,
                    // Dinamik tarih alanlarını da project ediyoruz ki '$addFields' kullanabilsin
                    currentYearMonth: 1,
                    previousYearMonth: 1,
                    lastYearSameMonth: 1
                }
            },

            // Dönem Tipi Kolonu Ekleme - ARTIK DOĞRU ÇALIŞACAK
            {
                $addFields: {
                    donemTipi: {
                        $switch: {
                            branches: [
                                {
                                    // SATIS_DONEM doğrudan "currentYearMonth" ile karşılaştırılabilir
                                    case: { $eq: ["$SATIS_DONEM", "$currentYearMonth"] },
                                    then: "BuAy"
                                },
                                {
                                    // SATIS_DONEM doğrudan "previousYearMonth" ile karşılaştırılabilir
                                    case: { $eq: ["$SATIS_DONEM", "$previousYearMonth"] },
                                    then: "GecenAy"
                                },
                                {
                                    // SATIS_DONEM doğrudan "lastYearSameMonth" ile karşılaştırılabilir
                                    case: { $eq: ["$SATIS_DONEM", "$lastYearSameMonth"] },
                                    then: "GecenYilAyniAy"
                                }
                            ],
                            default: "Diğer" // Beklenmeyen durumlar için
                        }
                    }
                }
            },

            {
                $project: {
                    previousYearMonth: 0,
                    lastYearSameMonth: 0,
                    currentYearMonth: 0,
                }
            },
            // Sonuçları sırala
            {
                $sort: {
                    SATIS_DONEM: -1
                }
            }
        ]
        try {
            let resp = {};
            const collection = await getCollection("tourai.data.fact.sales_hotelsdomestic");
            const data = await collection.aggregate(pipeline).toArray();
            resp = {
                data: data.length > 0 ? data : [],
                elapsed: Date.now() - dtBOP,
            }
            return res.status(200).json({ ...resp });
        }    catch(error) {
            return res.status(500).json({ error: "Database error", details: error.message });
        } 
    } else if (w === "tophotelsbydonem") {
        const limit = req.query.limit ? parseInt(req.query.limit, 10) : 10;
        const pipelinestg1 = [
            {
            $match: (() => {
                const { donem } = req.query;
                let match = {};
                if (donem) {
                if (/^\d{4}$/.test(donem)) {
                    match["SATIS_DONEM"] = { $regex: `^${donem}-` };
                } else if (/^\d{4}-\d{2}$/.test(donem)) {
                    match["SATIS_DONEM"] = donem;
                }
                } else {
                const currentYear = new Date().getFullYear();
                match["SATIS_DONEM"] = { $regex: `^${currentYear}-` };
                }
                return match;
            })()
            },
            {
            $group: {
                _id: "$TESIS_ID",
                toplamRezvAdedi: { $sum: "$REZV" },
                toplamCiro: { $sum: "$tTutar" }
            }
            },
            {
            $project: {
                _id: 0,
                TESIS_ID: "$_id",
                toplamRezvAdedi: 1,
                toplamCiro: 1
            }
            },
            {
            $sort: {
                toplamCiro: -1
            }
            },
            { $limit: limit }
        ]
        try {
            let resp = {};
            const collection = await getCollection("tourai.data.fact.sales_hotelsdomestic");
            const data = await collection.aggregate(pipelinestg1).toArray();
            const tesisIDs = data.map(item => item.TESIS_ID);
            const pipeline = [
                {
                    $match: {
                        TESIS_ID: { $in: tesisIDs }
                    }
                },
                {
                    $project: {
                        _id: 0,
                        TESIS_ID: 1,
                        TESIS_ADI: 1,
                        BOLGE_ADI: 1,
                        KATEGORI: 1,
                        ALT_BOLGE_ADI: 1,
                        BOLGE_DETAY: 1
                    }
                }
            ];
            const collection2 = await getCollection("tourai.data.dim.hotelsdomestic");
            const data2 = await collection2.aggregate(pipeline).toArray();
            data.forEach(item => {
                const matchingItem = data2.find(i => i.TESIS_ID === item.TESIS_ID);
                if (matchingItem) {
                    item.TESIS_ADI = matchingItem.TESIS_ADI;
                    item.BOLGE_ADI = matchingItem.BOLGE_ADI;
                    item.KATEGORI = matchingItem.KATEGORI;
                    item.ALT_BOLGE_ADI = matchingItem.ALT_BOLGE_ADI;
                    item.BOLGE_DETAY = matchingItem.BOLGE_DETAY;
                }
            });
            resp = {
                data: data.length > 0 ? data : [],
                // data2,
                elapsed: Date.now() - dtBOP,
            }
            return res.status(200).json({ ...resp });
        }    catch(error) {
            return res.status(500).json({ error: "Database error", details: error.message });
        } 
    } else if (w === "inactive") {
        query = { status: "inactive" };
        try {
            const collection = await getCollection("hotels");
            const data = await collection.find(query).toArray();
            return res.status(200).json({ data });
        } catch (error) {
            return res.status(500).json({ error: "Database error", details: error.message });
        }
    } else {
            return res.status(500).json({ error: "intent not found error" });
    }
    // Diğer w değerleri için ek sorgular ekleyebilirsin

  }

  // Hotel detail endpoint - /api/hotels/[hotelId]
  if (slug.length === 1 && !isNaN(parseInt(slug[0]))) {
    const hotelId = parseInt(slug[0]);

    try {
      const collection = await getCollection("tourai.data.dim.hotelsdomestic");
      const hotel = await collection.findOne({ TESIS_ID: hotelId });

      if (!hotel) {
        return res.status(404).json({ error: "Hotel not found" });
      }

      return res.status(200).json({
        data: hotel,
        elapsed: Date.now() - dtBOP
      });
    } catch (error) {
      return res.status(500).json({ error: "Database error", details: error.message });
    }
  }

  // Hotel sales data endpoint - /api/hotels/[hotelId]/sales
  if (slug.length === 2 && !isNaN(parseInt(slug[0])) && slug[1] === 'sales') {
    const hotelId = parseInt(slug[0]);
    const { period, limit = 12 } = req.query;

    try {
      const collection = await getCollection("tourai.data.fact.sales_hotelsdomestic");

      let matchQuery = { TESIS_ID: hotelId };

      // Period filtering
      if (period) {
        if (/^\d{4}$/.test(period)) {
          // Year format: 2024
          matchQuery["SATIS_DONEM"] = { $regex: `^${period}-` };
        } else if (/^\d{4}-\d{2}$/.test(period)) {
          // Month format: 2024-01
          matchQuery["SATIS_DONEM"] = period;
        }
      }

      const pipeline = [
        { $match: matchQuery },
        {
          $group: {
            _id: "$SATIS_DONEM",
            totalReservations: { $sum: "$REZV" },
            totalRevenue: { $sum: "$tTutar" },
            totalCost: { $sum: "$tMaliyet" },
            totalPaid: { $sum: "$tOdenen" },
            totalGuests: { $sum: { $add: ["$nYETISKIN", "$nCocuk"] } },
            totalNights: { $sum: "$REZV_GUNSAYISI" },
            avgRevenuePerReservation: { $avg: "$tTutar" }
          }
        },
        { $sort: { _id: 1 } },
        { $limit: parseInt(limit) }
      ];

      const salesData = await collection.aggregate(pipeline).toArray();

      // Calculate trends
      const trends = salesData.map((item, index) => {
        if (index === 0) return { ...item, revenueGrowth: 0, reservationGrowth: 0 };

        const prevItem = salesData[index - 1];
        const revenueGrowth = prevItem.totalRevenue > 0
          ? ((item.totalRevenue - prevItem.totalRevenue) / prevItem.totalRevenue * 100).toFixed(2)
          : 0;
        const reservationGrowth = prevItem.totalReservations > 0
          ? ((item.totalReservations - prevItem.totalReservations) / prevItem.totalReservations * 100).toFixed(2)
          : 0;

        return {
          ...item,
          revenueGrowth: parseFloat(revenueGrowth),
          reservationGrowth: parseFloat(reservationGrowth)
        };
      });

      return res.status(200).json({
        data: trends,
        elapsed: Date.now() - dtBOP
      });
    } catch (error) {
      return res.status(500).json({ error: "Database error", details: error.message });
    }
  }

  // Hotel web traffic data endpoint - /api/hotels/[hotelId]/traffic
  if (slug.length === 2 && !isNaN(parseInt(slug[0])) && slug[1] === 'traffic') {
    const hotelId = parseInt(slug[0]);
    const { period, limit = 12 } = req.query;

    try {
      const collection = await getCollection("tourai.data.fact.ga4_hotelsdomestic");

      let matchQuery = { TESIS_ID: hotelId };

      // Period filtering
      if (period) {
        if (/^\d{4}$/.test(period)) {
          // Year format: 2024
          matchQuery["TARIH"] = { $regex: `^${period}-` };
        } else if (/^\d{4}-\d{2}$/.test(period)) {
          // Month format: 2024-01
          matchQuery["TARIH"] = period;
        }
      }

      const pipeline = [
        { $match: matchQuery },
        {
          $group: {
            _id: "$TARIH",
            totalUsers: { $sum: "$TOTAL_USERS_SUM" },
            totalItemsViewed: { $sum: "$TOTAL_ITEMS_VIEWED" },
            totalRevenue: { $sum: "$TOTAL_ITEM_REVENUE" },
            avgRevenuePerUser: {
              $avg: {
                $cond: [
                  { $gt: ["$TOTAL_USERS_SUM", 0] },
                  { $divide: ["$TOTAL_ITEM_REVENUE", "$TOTAL_USERS_SUM"] },
                  0
                ]
              }
            }
          }
        },
        { $sort: { _id: 1 } },
        { $limit: parseInt(limit) }
      ];

      const trafficData = await collection.aggregate(pipeline).toArray();

      // Calculate trends
      const trends = trafficData.map((item, index) => {
        if (index === 0) return { ...item, userGrowth: 0, viewGrowth: 0 };

        const prevItem = trafficData[index - 1];
        const userGrowth = prevItem.totalUsers > 0
          ? ((item.totalUsers - prevItem.totalUsers) / prevItem.totalUsers * 100).toFixed(2)
          : 0;
        const viewGrowth = prevItem.totalItemsViewed > 0
          ? ((item.totalItemsViewed - prevItem.totalItemsViewed) / prevItem.totalItemsViewed * 100).toFixed(2)
          : 0;

        return {
          ...item,
          userGrowth: parseFloat(userGrowth),
          viewGrowth: parseFloat(viewGrowth)
        };
      });

      return res.status(200).json({
        data: trends,
        elapsed: Date.now() - dtBOP
      });
    } catch (error) {
      return res.status(500).json({ error: "Database error", details: error.message });
    }
  }

  // Hotel search endpoint - /api/hotels/search
  if (slug[0] === 'search') {
    const {
      q = '',
      category = '',
      region = '',
      subregion = '',
      listed = '',
      status = '',
      page = 1,
      limit = 20,
      sortBy = 'TESIS_ADI',
      sortOrder = 'asc'
    } = req.query;

    try {
      const collection = await getCollection("tourai.data.dim.hotelsdomestic");

      // Build search query
      let matchQuery = {};

      // Text search
      if (q) {
        matchQuery.$or = [
          { TESIS_ADI: { $regex: q, $options: 'i' } },
          { BOLGE_ADI: { $regex: q, $options: 'i' } },
          { ALT_BOLGE_ADI: { $regex: q, $options: 'i' } },
          { BOLGE_DETAY: { $regex: q, $options: 'i' } }
        ];
      }

      // Category filter
      if (category) {
        matchQuery.KATEGORI = { $regex: category, $options: 'i' };
      }

      // Region filter
      if (region) {
        matchQuery.BOLGE_ADI = { $regex: region, $options: 'i' };
      }

      // Sub-region filter
      if (subregion) {
        matchQuery.ALT_BOLGE_ADI = { $regex: subregion, $options: 'i' };
      }

      // Listed filter
      if (listed) {
        matchQuery.WEBSITE_LISTED = listed.toUpperCase();
      }

      // Status filter
      if (status) {
        matchQuery.TESIS_ONAY = { $regex: status, $options: 'i' };
      }

      // Calculate pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      // Build sort object
      const sortObj = {};
      sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Execute search with pagination
      const [hotels, totalCount] = await Promise.all([
        collection
          .find(matchQuery)
          .sort(sortObj)
          .skip(skip)
          .limit(limitNum)
          .toArray(),
        collection.countDocuments(matchQuery)
      ]);

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / limitNum);
      const hasNextPage = pageNum < totalPages;
      const hasPrevPage = pageNum > 1;

      return res.status(200).json({
        data: hotels,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          limit: limitNum,
          hasNextPage,
          hasPrevPage
        },
        filters: {
          q,
          category,
          region,
          subregion,
          listed,
          status,
          sortBy,
          sortOrder
        },
        elapsed: Date.now() - dtBOP
      });
    } catch (error) {
      return res.status(500).json({ error: "Database error", details: error.message });
    }
  }

  // Hotel filters endpoint - /api/hotels/filters
  if (slug[0] === 'filters') {
    try {
      const collection = await getCollection("tourai.data.dim.hotelsdomestic");

      const [categories, regions, subregions, statuses] = await Promise.all([
        collection.distinct("KATEGORI"),
        collection.distinct("BOLGE_ADI"),
        collection.distinct("ALT_BOLGE_ADI"),
        collection.distinct("TESIS_ONAY")
      ]);

      return res.status(200).json({
        data: {
          categories: categories.filter(Boolean).sort(),
          regions: regions.filter(Boolean).sort(),
          subregions: subregions.filter(Boolean).sort(),
          statuses: statuses.filter(Boolean).sort(),
          listedOptions: ['TRUE', 'FALSE']
        },
        elapsed: Date.now() - dtBOP
      });
    } catch (error) {
      return res.status(500).json({ error: "Database error", details: error.message });
    }
  }

  // Diğer slug değerleri için default response
  res.status(404).json({ error: "Not found" });
}
