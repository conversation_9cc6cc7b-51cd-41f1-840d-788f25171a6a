import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import ProtectedRoute from '@/components/ProtectedRoute';
import Layout from '@/components/layouts/Layout';
import {
  Search,
  Filter,
  MapPin,
  Star,
  Phone,
  Globe,
  Building,
  ChevronLeft,
  ChevronRight,
  Eye,
  X,
  Grid3X3,
  List,
  RotateCcw,
  Settings
} from 'lucide-react';
import {
  getStoredPreferences,
  savePreferences,
  clearAllPreferences,
  DEFAULT_PREFERENCES
} from '@/lib/utils/localStorage';

const HotelList = () => {
  const router = useRouter();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState(DEFAULT_PREFERENCES.filters);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState(DEFAULT_PREFERENCES.viewMode); // 'card' or 'table'
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  // Load preferences from localStorage on component mount
  useEffect(() => {
    const loadPreferences = () => {
      const stored = getStoredPreferences();
      setSelectedFilters(stored.filters);
      setViewMode(stored.viewMode);
      setShowFilters(stored.showFilters);
      setPreferencesLoaded(true);
    };

    loadPreferences();
    fetchFilters();
  }, []);

  // Fetch hotels when preferences are loaded or filters change
  useEffect(() => {
    if (preferencesLoaded) {
      fetchHotels();
    }
  }, [currentPage, selectedFilters, searchQuery, preferencesLoaded]);

  // Save preferences to localStorage when they change
  useEffect(() => {
    if (preferencesLoaded) {
      savePreferences({
        filters: selectedFilters,
        viewMode,
        showFilters
      });
    }
  }, [selectedFilters, viewMode, showFilters, preferencesLoaded]);

  const fetchFilters = async () => {
    try {
      const response = await fetch('/api/hotels/filters');
      if (response.ok) {
        const result = await response.json();
        setFilters(result.data);
      }
    } catch (error) {
      console.error('Error fetching filters:', error);
    }
  };

  const fetchHotels = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        q: searchQuery,
        ...selectedFilters
      });

      const response = await fetch(`/api/hotels/search?${params}`);
      if (response.ok) {
        const result = await response.json();
        setHotels(result.data || []);
        setPagination(result.pagination || {});
      }
    } catch (error) {
      console.error('Error fetching hotels:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchHotels();
  };

  const handleFilterChange = (key, value) => {
    setSelectedFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSelectedFilters(DEFAULT_PREFERENCES.filters);
    setSearchQuery('');
    setCurrentPage(1);
  };

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'card' ? 'table' : 'card');
  };

  const clearAllUserPreferences = () => {
    clearAllPreferences();
    setSelectedFilters(DEFAULT_PREFERENCES.filters);
    setViewMode(DEFAULT_PREFERENCES.viewMode);
    setShowFilters(DEFAULT_PREFERENCES.showFilters);
    setSearchQuery('');
    setCurrentPage(1);
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Onaylandı': return 'bg-green-100 text-green-800';
      case 'Reddedildi': return 'bg-red-100 text-red-800';
      case 'Beklemede': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderPagination = () => {
    if (!pagination.totalPages || pagination.totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-center space-x-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(currentPage - 1)}
          disabled={!pagination.hasPrevPage}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>

        {startPage > 1 && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(1)}
            >
              1
            </Button>
            {startPage > 2 && <span className="px-2">...</span>}
          </>
        )}

        {pages.map(page => (
          <Button
            key={page}
            variant={page === currentPage ? "default" : "outline"}
            size="sm"
            onClick={() => setCurrentPage(page)}
          >
            {page}
          </Button>
        ))}

        {endPage < pagination.totalPages && (
          <>
            {endPage < pagination.totalPages - 1 && <span className="px-2">...</span>}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(pagination.totalPages)}
            >
              {pagination.totalPages}
            </Button>
          </>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(currentPage + 1)}
          disabled={!pagination.hasNextPage}
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  return (
    <ProtectedRoute>
      <Layout 
        isDrawerOpen={isDrawerOpen} 
        toggleDrawer={toggleDrawer} 
        title="Hotel Listing"
        breadCrumbs={['Home', 'Hotels', 'List']}
      >
        <div className="space-y-6 max-w-7xl mx-auto">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Search Hotels</span>
                <div className="flex items-center space-x-2">
                  {/* View Mode Toggle */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleViewMode}
                    title={`Switch to ${viewMode === 'card' ? 'table' : 'card'} view`}
                  >
                    {viewMode === 'card' ? (
                      <List className="h-4 w-4" />
                    ) : (
                      <Grid3X3 className="h-4 w-4" />
                    )}
                  </Button>

                  {/* Filters Toggle */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                  </Button>

                  {/* Clear Preferences */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllUserPreferences}
                    title="Clear all preferences"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search Bar */}
              <form onSubmit={handleSearch} className="flex space-x-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search hotels by name, location..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button type="submit">Search</Button>
              </form>

              {/* Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4 bg-gray-50 rounded-lg">
                  <Select value={selectedFilters.category || "all"} onValueChange={(value) => handleFilterChange('category', value === "all" ? "" : value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {filters.categories?.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedFilters.region || "all"} onValueChange={(value) => handleFilterChange('region', value === "all" ? "" : value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      {filters.regions?.map(region => (
                        <SelectItem key={region} value={region}>{region}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedFilters.listed || "all"} onValueChange={(value) => handleFilterChange('listed', value === "all" ? "" : value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Listed Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="TRUE">Listed</SelectItem>
                      <SelectItem value="FALSE">Not Listed</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={selectedFilters.status || "all"} onValueChange={(value) => handleFilterChange('status', value === "all" ? "" : value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {filters.statuses?.map(status => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={selectedFilters.sortBy} onValueChange={(value) => handleFilterChange('sortBy', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sort By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TESIS_ADI">Name</SelectItem>
                      <SelectItem value="BOLGE_ADI">Region</SelectItem>
                      <SelectItem value="KATEGORI">Category</SelectItem>
                      <SelectItem value="TESIS_KAPASITE">Capacity</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex space-x-2">
                    <Select value={selectedFilters.sortOrder} onValueChange={(value) => handleFilterChange('sortOrder', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Order" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">A-Z</SelectItem>
                        <SelectItem value="desc">Z-A</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="sm" onClick={clearFilters}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {/* Results Summary */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  {pagination.totalCount ? `${pagination.totalCount} hotels found` : 'No hotels found'}
                </span>
                {pagination.totalCount > 0 && (
                  <span>
                    Page {pagination.currentPage} of {pagination.totalPages}
                  </span>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Hotels Grid */}
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              {/* Hotels Display */}
              {viewMode === 'card' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {hotels.map((hotel) => (
                  <Card
                    key={hotel.TESIS_ID}
                    className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => router.push(`/hotels-domestic/${hotel.TESIS_ID}`)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-lg line-clamp-2">{hotel.TESIS_ADI}</CardTitle>
                        <div className="flex flex-col items-end space-y-1">
                          <Badge className={getStatusColor(hotel.TESIS_ONAY)}>
                            {hotel.TESIS_ONAY}
                          </Badge>
                          {hotel.WEBSITE_LISTED === 'TRUE' && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700">
                              <Globe className="h-3 w-3 mr-1" />
                              Listed
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {/* Category */}
                      {hotel.KATEGORI && (
                        <div className="flex items-center space-x-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-medium">{hotel.KATEGORI}</span>
                        </div>
                      )}

                      {/* Location */}
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <span>{hotel.BOLGE_ADI}</span>
                        {hotel.ALT_BOLGE_ADI && (
                          <>
                            <span>•</span>
                            <span>{hotel.ALT_BOLGE_ADI}</span>
                          </>
                        )}
                      </div>

                      {/* Details */}
                      {hotel.BOLGE_DETAY && (
                        <div className="text-sm text-gray-500 line-clamp-2">
                          {hotel.BOLGE_DETAY}
                        </div>
                      )}

                      {/* Capacity */}
                      {hotel.TESIS_KAPASITE && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Building className="h-4 w-4 text-gray-400" />
                          <span>{hotel.TESIS_KAPASITE} rooms</span>
                        </div>
                      )}

                      Contact
                      {hotel.TEL && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Phone className="h-4 w-4" />
                          <span>{hotel.TEL}</span>
                        </div>
                      )}

                      {/* Rating */}
                      {hotel.TESIS_PUANI > 0 && (
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < Math.floor(hotel.TESIS_PUANI / 2)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium">{hotel.TESIS_PUANI}/10</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
                </div>
              ) : (
                /* Table View */
                <Card>
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Hotel</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Listed</TableHead>
                          <TableHead>Capacity</TableHead>
                          {/* <TableHead>Contact</TableHead> */}
                          <TableHead>Rating</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {hotels.map((hotel) => (
                          <TableRow
                            key={hotel.TESIS_ID}
                            className="cursor-pointer hover:bg-gray-50"
                            onClick={() => router.push(`/hotels-domestic/${hotel.TESIS_ID}`)}
                          >
                            <TableCell>
                              <div>
                                <div className="font-semibold">{hotel.TESIS_ADI}</div>
                                {hotel.BOLGE_DETAY && (
                                  <div className="text-sm text-gray-500 line-clamp-1">
                                    {hotel.BOLGE_DETAY}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {hotel.KATEGORI && (
                                <Badge variant="outline">{hotel.KATEGORI}</Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div>{hotel.BOLGE_ADI}</div>
                                {hotel.ALT_BOLGE_ADI && (
                                  <div className="text-gray-500">{hotel.ALT_BOLGE_ADI}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(hotel.TESIS_ONAY)}>
                                {hotel.TESIS_ONAY}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {hotel.WEBSITE_LISTED === 'TRUE' ? (
                                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                  <Globe className="h-3 w-3 mr-1" />
                                  Yes
                                </Badge>
                              ) : (
                                <span className="text-gray-400">No</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {hotel.TESIS_KAPASITE ? (
                                <div className="flex items-center space-x-1">
                                  <Building className="h-4 w-4 text-gray-400" />
                                  <span>{hotel.TESIS_KAPASITE}</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                            {/* <TableCell>
                              {hotel.TEL ? (
                                <div className="flex items-center space-x-1">
                                  <Phone className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm">{hotel.TEL}</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell> */}
                            <TableCell>
                              {hotel.TESIS_PUANI > 0 ? (
                                <div className="flex items-center space-x-1">
                                  <Star className="h-4 w-4 text-yellow-400" />
                                  <span className="text-sm">{hotel.TESIS_PUANI}/10</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              )}

              {/* No Results */}
              {hotels.length === 0 && !loading && (
                <div className="text-center py-12">
                  <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No hotels found</h3>
                  <p className="text-gray-500 mb-4">
                    Try adjusting your search criteria or filters
                  </p>
                  <Button onClick={clearFilters}>Clear all filters</Button>
                </div>
              )}

              {/* Pagination */}
              {renderPagination()}
            </>
          )}
        </div>
      </Layout>
    </ProtectedRoute>
  );
};

export default HotelList;
