import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

// .env dosyasını yükle
dotenv.config();

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            // { sqlTable: 'q_tesis_listesi', mongoCollection: 'tourai.data.dim.hotelsdomestic' },
            // { sqlTable: 'q_ga4_stats', mongoCollection: 'tourai.data.fact.ga4_hotelsdomestic' },
            // { sqlTable: 'q_salesPerItems', mongoCollection: 'tourai.data.fact.sales_hotelsdomestic' },
            { sqlTable: 'q_summary', mongoCollection: 'tourai.data.dim.hotelsdomestic_summary' },
        ];

        console.log('SQLite veritabanı erişiliyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');

        // Her tablo için işlem yap
        const syncResults = [];

        for (let i = 0; i < tables2Sync.length; i++) {
            const tableInfo = tables2Sync[i];
            const tableStartTime = Date.now();

            console.log(`\n[${i + 1}/${tables2Sync.length}] ${tableInfo.sqlTable} -> ${tableInfo.mongoCollection} aktarımı başlıyor...`);

            try {
                // SQL tablosundan verileri oku
                console.log(`  📖 ${tableInfo.sqlTable} tablosundan veriler okunuyor...`);
                const sqlData = await db.all(`SELECT * FROM "${tableInfo.sqlTable}"`);

                if (!sqlData || sqlData.length === 0) {
                    console.log(`  ⚠️  ${tableInfo.sqlTable} tablosunda veri bulunamadı, atlanıyor...`);
                    syncResults.push({
                        table: tableInfo.sqlTable,
                        collection: tableInfo.mongoCollection,
                        recordCount: 0,
                        duration: Date.now() - tableStartTime,
                        status: 'skipped'
                    });
                    continue;
                }

                console.log(`  📄 ${sqlData.length} kayıt bulundu`);

                // MongoDB collection'ı temizle
                const collectionName = tableInfo.mongoCollection;
                var collection = mongoDb.collection(collectionName);

                if (collectionName === 'tourai.data.dim.hotelsdomestic') {
                    console.log(`  🧹 ${collectionName} collection'ı temizleniyor (drop edilmeden)...`);
                    // Tüm kayıtları sil
                    await collection.deleteMany({});
                    console.log(`  ✅ ${collectionName} collection'ındaki tüm kayıtlar silindi.`);
                    // Tüm indexleri sil
                    try {
                        const indexes = await collection.indexes();
                        for (const idx of indexes) {
                            if (idx.name !== '_id_') {
                                await collection.dropIndex(idx.name);
                                console.log(`    🗑️  Index silindi: ${idx.name}`);
                            }
                        }
                    } catch (err) {
                        console.log(`    ⚠️  Index silme hatası: ${err.message}`);
                    }
                    // Collection yeniden oluşturulmasına gerek yok
                } else {
                    console.log(`  🗑️  ${collectionName} collection'ı drop ediliyor...`);
                    try {
                        await collection.drop();
                        console.log(`  ✅ ${collectionName} collection'ı silindi.`);
                    } catch (err) {
                        if (err.codeName === 'NamespaceNotFound') {
                            console.log(`  ℹ️  ${collectionName} collection'ı zaten yoktu.`);
                        } else {
                            throw err;
                        }
                    }
                    // Yeniden oluşturmak için bir dummy insert ve delete (MongoDB'de collection otomatik oluşur)
                    await mongoDb.createCollection(collectionName).catch(() => {});
                    console.log(`  📁 ${collectionName} collection'ı yeniden oluşturuldu.`);
                    const newCollection = mongoDb.collection(collectionName);
                    // Bundan sonra newCollection kullanılmalı
                    // Ancak kodun devamında collection değişkeni kullanıldığı için, referansı güncelle
                    collection = newCollection;
                }

                // Verileri MongoDB'ye yaz
                console.log(`  📝 Veriler ${tableInfo.mongoCollection} collection'ına yazılıyor...`);

                // q_summary tablosu için özel işlem
                let processedData = sqlData;
                if (tableInfo.sqlTable === 'q_summary') {
                    console.log(`  🔄 q_summary tablosu için JSON parse işlemi yapılıyor...`);
                    processedData = sqlData.map((record, index) => {
                        const processedRecord = { ...record };

                        // Satislar kolonunu JSON parse et
                        if (record.Satislar) {
                            try {
                                let satislarStr = record.Satislar;
                                if (typeof satislarStr === 'string') {
                                    // "İ" anahtarının değerindeki tüm \t ve özel karakterleri temizle
                                    satislarStr = satislarStr.replace(
                                        /("İ"\s*:\s*")([^"]*)"/g,
                                        (match, p1, p2) => {
                                            const cleaned = p2.replace(/[\t\n\r]/g, '');
                                            return `${p1}${cleaned}"`;
                                        }
                                    );
                                    // Kontrol karakterlerini temizle (0x00-0x1F ve 0x7F)
                                    satislarStr = satislarStr.replace(/[\u0000-\u001F\u007F]/g, '');

                                    // JSON'a çevir
                                    let satislarObj = JSON.parse(satislarStr);

                                    // Oda_Tipi anahtarı için alfanumerik dışı karakterleri temizle
                                    if (Array.isArray(satislarObj)) {
                                        satislarObj.forEach(item => {
                                            if (item.Oda_Tipi && typeof item.Oda_Tipi === 'string') {
                                                item.Oda_Tipi = item.Oda_Tipi.replace(/[^a-zA-Z0-9]/g, '');
                                            }
                                        });
                                    } else if (satislarObj && typeof satislarObj === 'object') {
                                        if (satislarObj.Oda_Tipi && typeof satislarObj.Oda_Tipi === 'string') {
                                            satislarObj.Oda_Tipi = satislarObj.Oda_Tipi.replace(/[^a-zA-Z0-9]/g, '');
                                        }
                                    }
                                    processedRecord.Satislar = satislarObj;
                                } else {
                                    processedRecord.Satislar = satislarStr;
                                }
                            } catch (parseError) {
                                console.log(`    ⚠️  Kayıt ${index + 1}: Satislar JSON parse hatası, orijinal değer korunuyor`);
                                processedRecord.Satislar = record.Satislar;
                                // Hatalı satislar değerini dosyaya kaydet
                                try {
                                    const errorFileName = `hata_kayit${index + 1}.json`;
                                    fs.writeFileSync(errorFileName, String(record.Satislar), 'utf8');
                                    console.log(`    💾 Hatalı Satislar değeri ${errorFileName} dosyasına kaydedildi.`);
                                } catch (fileError) {
                                    console.log(`    ⚠️  Hatalı Satislar dosyaya yazılamadı: ${fileError.message}`);
                                }
                            }
                        }
                        // WebTrafik kolonunu JSON parse et
                        if (record.WebTrafik) {
                            try {
                                processedRecord.WebTrafik = JSON.parse(record.WebTrafik);
                            } catch (parseError) {
                                console.log(`    ⚠️  Kayıt ${index + 1}: WebTrafik JSON parse hatası, orijinal değer korunuyor`);
                                // Orijinal değeri koru
                                processedRecord.WebTrafik = record.WebTrafik;
                            }
                        }

                        return processedRecord;
                    });
                    console.log(`  ✅ JSON parse işlemi tamamlandı`);
                }

                // Batch insert için verileri hazırla
                const batchSize = 1000;
                let insertedCount = 0;

                for (let j = 0; j < processedData.length; j += batchSize) {
                    const batch = processedData.slice(j, j + batchSize);
                    await collection.insertMany(batch);
                    insertedCount += batch.length;

                    // İlerleme göster
                    if (insertedCount % 5000 === 0 || insertedCount === processedData.length) {
                        console.log(`    📊 ${insertedCount}/${processedData.length} kayıt yazıldı...`);
                    }
                }

                const tableElapsedTime = Date.now() - tableStartTime;
                console.log(`  ✅ ${insertedCount} kayıt başarıyla aktarıldı. (${tableElapsedTime}ms)`);

                syncResults.push({
                    table: tableInfo.sqlTable,
                    collection: tableInfo.mongoCollection,
                    recordCount: insertedCount,
                    duration: tableElapsedTime,
                    status: 'success'
                });

            } catch (tableError) {
                const tableElapsedTime = Date.now() - tableStartTime;
                console.error(`  ❌ ${tableInfo.sqlTable} aktarımında hata:`, tableError.message);

                syncResults.push({
                    table: tableInfo.sqlTable,
                    collection: tableInfo.mongoCollection,
                    recordCount: 0,
                    duration: tableElapsedTime,
                    status: 'error',
                    error: tableError.message
                });
            }
        }

        // Spatial index oluşturma işlemleri
        console.log(`\n🗺️  SPATIAL INDEX İŞLEMLERİ`);
        console.log(`=====================================`);

        const spatialCollections = [
            'tourai.data.dim.hotelsdomestic',
            'tourai.data.dim.hotelsdomestic_summary'
        ];

        for (const collectionName of spatialCollections) {
            const spatialStartTime = Date.now();
            console.log(`\n📍 ${collectionName} için spatial index işlemi başlıyor...`);

            try {
                const collection = mongoDb.collection(collectionName);

                // Önce koleksiyonda kayıt var mı kontrol et
                const recordCount = await collection.countDocuments();
                if (recordCount === 0) {
                    console.log(`  ⚠️  ${collectionName} koleksiyonunda kayıt bulunamadı, spatial index atlanıyor...`);
                    continue;
                }

                console.log(`  📊 ${recordCount} kayıt bulundu`);
                console.log(`  🔄 Location alanları oluşturuluyor...`);

                // Location alanını oluştur
                const updateResult = await collection.updateMany(
                    {},
                    [
                        {
                            $set: {
                                location: {
                                    type: "Point",
                                    coordinates: [
                                        { $toDouble: "$LONGITUDE" },
                                        { $toDouble: "$LATITUDE" }
                                    ]
                                }
                            }
                        }
                    ]
                );

                console.log(`  ✅ ${updateResult.modifiedCount} kayıt güncellendi`);
                // Hatalı/eksik location alanlarını null yap
                const invalidLocationResult = await collection.updateMany(
                    {
                        $or: [
                            { LATITUDE: { $lt: -90 } },
                            { LATITUDE: { $gt: 90 } },
                            { LONGITUDE: { $lt: -180 } },
                            { LONGITUDE: { $gt: 180 } },
                            { LATITUDE: null },
                            { LONGITUDE: null }
                        ]
                    },
                    {
                        $set: { location: null }
                    }
                );
                console.log(`  ⚠️  ${invalidLocationResult.modifiedCount} hatalı/eksik location kaydı null yapıldı`);


                // Mevcut spatial index'leri kaldır
                console.log(`  🗑️  Mevcut spatial index'ler kontrol ediliyor...`);
                try {
                    const indexes = await collection.listIndexes().toArray();
                    const spatialIndexes = indexes.filter(index =>
                        index.key && index.key.location && index.key.location === '2dsphere'
                    );

                    for (const spatialIndex of spatialIndexes) {
                        console.log(`    🗑️  Mevcut spatial index kaldırılıyor: ${spatialIndex.name}`);
                        await collection.dropIndex(spatialIndex.name);
                    }
                } catch (indexError) {
                    console.log(`    ⚠️  Index kontrol/kaldırma hatası (normal olabilir): ${indexError.message}`);
                }

                // Yeni spatial index oluştur
                console.log(`  🏗️  Yeni spatial index oluşturuluyor...`);
                await collection.createIndex(
                    { location: "2dsphere" },
                    { name: "location_2dsphere" }, 
                    { sparse: true }
                );

                const spatialElapsedTime = Date.now() - spatialStartTime;
                console.log(`  ✅ Spatial index başarıyla oluşturuldu. (${spatialElapsedTime}ms)`);

            } catch (spatialError) {
                const spatialElapsedTime = Date.now() - spatialStartTime;
                console.error(`  ❌ ${collectionName} spatial index hatası: ${spatialError.message} (${spatialElapsedTime}ms)`);
            }
        }

        // Özet raporu
        const totalElapsedTime = Date.now() - dtBop;
        console.log(`\n📊 AKTARIM ÖZETİ`);
        console.log(`=====================================`);
        console.log(`⏱️  Toplam süre: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
        console.log(`📋 Aktarılan tablolar:`);

        let totalRecords = 0;
        let successCount = 0;
        let errorCount = 0;
        let skippedCount = 0;

        syncResults.forEach((result, index) => {
            const statusIcon = result.status === 'success' ? '✅' :
                              result.status === 'error' ? '❌' : '⏭️';

            console.log(`  ${statusIcon} [${index + 1}] ${result.table} -> ${result.collection}`);
            console.log(`      📊 Kayıt sayısı: ${result.recordCount.toLocaleString()}`);
            console.log(`      ⏱️  Süre: ${result.duration}ms (${(result.duration/1000).toFixed(2)}s)`);

            if (result.error) {
                console.log(`      ⚠️  Hata: ${result.error}`);
            }

            totalRecords += result.recordCount;

            if (result.status === 'success') successCount++;
            else if (result.status === 'error') errorCount++;
            else skippedCount++;
        });

        console.log(`\n📈 GENEL İSTATİSTİKLER`);
        console.log(`=====================================`);
        console.log(`✅ Başarılı: ${successCount} tablo`);
        console.log(`❌ Hatalı: ${errorCount} tablo`);
        console.log(`⏭️  Atlanan: ${skippedCount} tablo`);
        console.log(`📊 Toplam aktarılan kayıt: ${totalRecords.toLocaleString()}`);
        console.log(`⚡ Ortalama hız: ${totalRecords > 0 ? Math.round(totalRecords / (totalElapsedTime / 1000)) : 0} kayıt/saniye`);

        // Bağlantıları kapat
        await db.close();
        await mongoClient.close();

        console.log(`\n🎉 Tüm işlemler tamamlandı!`);

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
