import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';

// .env dosyasını yükle
dotenv.config();

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            { sqlTable: 'q_tesis_listesi', mongoCollection: 'tourai.data.dim.hotelsdomestic' },
            { sqlTable: 'q_ga4_stats', mongoCollection: 'tourai.data.fact.ga4_hotelsdomestic' },
            { sqlTable: 'q_salesPerItems', mongoCollection: 'tourai.data.fact.sales_hotelsdomestic' },
            { sqlTable: 'q_summary', mongoCollection: 'tourai.data.dim.hotelsdomestic_summary' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');

        // Her tablo için işlem yap
        const syncResults = [];

        for (let i = 0; i < tables2Sync.length; i++) {
            const tableInfo = tables2Sync[i];
            const tableStartTime = Date.now();

            console.log(`\n[${i + 1}/${tables2Sync.length}] ${tableInfo.sqlTable} -> ${tableInfo.mongoCollection} aktarımı başlıyor...`);

            try {
                // SQL tablosundan verileri oku
                console.log(`  📖 ${tableInfo.sqlTable} tablosundan veriler okunuyor...`);
                const sqlData = await db.all(`SELECT * FROM "${tableInfo.sqlTable}"`);

                if (!sqlData || sqlData.length === 0) {
                    console.log(`  ⚠️  ${tableInfo.sqlTable} tablosunda veri bulunamadı, atlanıyor...`);
                    syncResults.push({
                        table: tableInfo.sqlTable,
                        collection: tableInfo.mongoCollection,
                        recordCount: 0,
                        duration: Date.now() - tableStartTime,
                        status: 'skipped'
                    });
                    continue;
                }

                console.log(`  📄 ${sqlData.length} kayıt bulundu`);

                // MongoDB collection'ı temizle
                const collection = mongoDb.collection(tableInfo.mongoCollection);
                console.log(`  🗑️  ${tableInfo.mongoCollection} collection'ı temizleniyor...`);
                await collection.deleteMany({});

                // Verileri MongoDB'ye yaz
                console.log(`  📝 Veriler ${tableInfo.mongoCollection} collection'ına yazılıyor...`);

                // Batch insert için verileri hazırla
                const batchSize = 1000;
                let insertedCount = 0;

                for (let j = 0; j < sqlData.length; j += batchSize) {
                    const batch = sqlData.slice(j, j + batchSize);
                    await collection.insertMany(batch);
                    insertedCount += batch.length;

                    // İlerleme göster
                    if (insertedCount % 5000 === 0 || insertedCount === sqlData.length) {
                        console.log(`    📊 ${insertedCount}/${sqlData.length} kayıt yazıldı...`);
                    }
                }

                const tableElapsedTime = Date.now() - tableStartTime;
                console.log(`  ✅ ${insertedCount} kayıt başarıyla aktarıldı. (${tableElapsedTime}ms)`);

                syncResults.push({
                    table: tableInfo.sqlTable,
                    collection: tableInfo.mongoCollection,
                    recordCount: insertedCount,
                    duration: tableElapsedTime,
                    status: 'success'
                });

            } catch (tableError) {
                const tableElapsedTime = Date.now() - tableStartTime;
                console.error(`  ❌ ${tableInfo.sqlTable} aktarımında hata:`, tableError.message);

                syncResults.push({
                    table: tableInfo.sqlTable,
                    collection: tableInfo.mongoCollection,
                    recordCount: 0,
                    duration: tableElapsedTime,
                    status: 'error',
                    error: tableError.message
                });
            }
        }

        // Özet raporu
        const totalElapsedTime = Date.now() - dtBop;
        console.log(`\n📊 AKTARIM ÖZETİ`);
        console.log(`=====================================`);
        console.log(`⏱️  Toplam süre: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
        console.log(`📋 Aktarılan tablolar:`);

        let totalRecords = 0;
        let successCount = 0;
        let errorCount = 0;
        let skippedCount = 0;

        syncResults.forEach((result, index) => {
            const statusIcon = result.status === 'success' ? '✅' :
                              result.status === 'error' ? '❌' : '⏭️';

            console.log(`  ${statusIcon} [${index + 1}] ${result.table} -> ${result.collection}`);
            console.log(`      📊 Kayıt sayısı: ${result.recordCount.toLocaleString()}`);
            console.log(`      ⏱️  Süre: ${result.duration}ms (${(result.duration/1000).toFixed(2)}s)`);

            if (result.error) {
                console.log(`      ⚠️  Hata: ${result.error}`);
            }

            totalRecords += result.recordCount;

            if (result.status === 'success') successCount++;
            else if (result.status === 'error') errorCount++;
            else skippedCount++;
        });

        console.log(`\n📈 GENEL İSTATİSTİKLER`);
        console.log(`=====================================`);
        console.log(`✅ Başarılı: ${successCount} tablo`);
        console.log(`❌ Hatalı: ${errorCount} tablo`);
        console.log(`⏭️  Atlanan: ${skippedCount} tablo`);
        console.log(`📊 Toplam aktarılan kayıt: ${totalRecords.toLocaleString()}`);
        console.log(`⚡ Ortalama hız: ${totalRecords > 0 ? Math.round(totalRecords / (totalElapsedTime / 1000)) : 0} kayıt/saniye`);

        // Bağlantıları kapat
        await db.close();
        await mongoClient.close();

        console.log(`\n🎉 Tüm işlemler tamamlandı!`);

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
