import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            { sqlTable: 'q_tesis_listesi', mongoCollection: 'tourai.data.dim.hotelsdomestic' },
            { sqlTable: 'q_ga4_stats', mongoCollection: 'tourai.data.fact.ga4_hotelsdomestic' },
            { sqlTable: 'q_salesPerItems', mongoCollection: 'tourai.data.fact.sales_hotelsdomestic' },
            { sqlTable: 'q_summary', mongoCollection: 'tourai.data.dim.hotelsdomestic_summary' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);
