import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
dotenv.config();
import { GoogleGenerativeAI } from '@google/generative-ai';
// import { GoogleGenAI } from "@google/genai";
// function sanitizeText(text) {
//     if (!text || typeof text !== 'string') {
//         return '';
//     }
//     return text.replace(/[\u2018\u2019\u201A\u201B\u2032\u2035]/g, "'") // tek tırnak
//         .replace(/[\u201C\u201D\u201E\u201F\u2033\u2036]/g, '"') // çift tırnak
//         .replace(/[\u2010-\u2015]/g, '-') // tireler
//         .replace(/\u2026/g, ' . ..') // üç nokta
//         .replace(/[\x00-\x1F\x7F-\x9F]/g, ' ') // Kontrol karakterlerini boşluk yap
//         .replace(/[^\x00-\x7F]/g, ' ')
//         .replace(/\s+/g, ' ') // Çoklu boşlukları tek boşluk haline getir
//         .replace(/[^\x00-\x7F]/g, ' ') // ASCII dışı kalanları da temizle (güvenlik için)
//         .trim();

//         // // Türkçe karakterleri ASCII karşılıklarına çevir
//         // .replace(/[çÇ]/g, 'c')
//         // .replace(/[ğĞ]/g, 'g')
//         // .replace(/[ıİ]/g, 'i')
//         // .replace(/[öÖ]/g, 'o')
//         // .replace(/[şŞ]/g, 's')
//         // .replace(/[üÜ]/g, 'u')
// }
 
const ollamaModel = 'nomic-embed-text:latest';
const modeTest = false;
const modeTestLen = 10;

export const vectorEmbeddings = {
    google_v1: async ({
        text,
        genAIpack,
        model = "text-embedding-004",
    }) => {
        if (!text) {
            console.error('google: Error no text');
            return null
        };
        try {
            // API anahtarı ile Google AI istemcisini başlat
            const GEMINI_API_KEY = process.env.GOOGLE_AI_API_KEY;
            const genAI = genAIpack || new GoogleGenerativeAI(GEMINI_API_KEY);
            // text-embedding-004 modelini al
            const embeddingModel = genAI.getGenerativeModel({ model });
            // Embedding oluştur
            const result = await embeddingModel.embedContent(text);
            // Embedding değerlerini çıkar
            if (result && result.embedding && Array.isArray(result.embedding.values)) {
                return result.embedding.values;
            } else {
                console.error('Unexpected response format from Google AI:', result);
                return null;
            }
        } catch (error) {
            console.error('Error generating embedding with Google AI:', error);
            return null;
        }
    },
    google_v1_: async ({
        text,
        genAIpack,
        model = "text-embedding-004",
    }) => {
        if (!text) {
            console.error('google: Error no text');
            return null
        };
        try {
            // API anahtarı ile Google AI istemcisini başlat
            const GEMINI_API_KEY = process.env.GOOGLE_AI_API_KEY;
            const genAI = genAIpack || new GoogleGenerativeAI(GEMINI_API_KEY);
            // text-embedding-004 modelini al
            const embeddingModel = genAI.getGenerativeModel({ model });
            // Embedding oluştur
            const result = await embeddingModel.embedContent(text);
            // Embedding değerlerini çıkar
            if (result && result.embedding && Array.isArray(result.embedding.values)) {
                return result.embedding.values;
            } else {
                console.error('Unexpected response format from Google AI:', result);
                return null;
            }
        } catch (error) {
            console.error('Error generating embedding with Google AI:', error);
            return null;
        }
    },
    // google_v2: async ({
    //     text,
    //     genAIpack,
    //     // model = "text-embedding-004",
    //     model = "gemini-embedding-exp-03-07", //'gemini-embedding-exp-03-07'  "text-embedding-004"
    //     debug = false,
    // }) => {
    //     if (!text) {
    //         console.error('google: Error no text');
    //         return null
    //     };
    //     try {
    //         // API anahtarı ile Google AI istemcisini başlat
    //         const MAX_INPUT_LENGTH = 1024 * 3; // yaklaşık 1024 token civarı
    //         const truncatedText = (text).slice(0, MAX_INPUT_LENGTH);
    //         // Unicode karakterleri düz ASCII'ye çevir - daha kapsamlı temizleme
    //         const safeText = (sanitizeText(truncatedText));
    //         const GEMINI_API_KEY = process.env.GOOGLE_AI_API_KEY;
    //         const genAI = genAIpack || new GoogleGenAI({
    //             apiKey: GEMINI_API_KEY,
    //         });
    //         const aiPost = {
    //             model: model,
    //             contents: safeText,
    //             config: {
    //                 taskType: "SEMANTIC_SIMILARITY",
    //             }
    //         };
    //         // debug && console.log('aiPost', JSON.stringify(aiPost));
    //         const result = await genAI.models.embedContent(aiPost);

    //         // Embedding değerlerini çıkar
    //         if (result && result.embeddings && Array.isArray(result.embeddings[0].values)) {
    //             return result.embeddings[0].values;
    //         } else {
    //             console.error('Unexpected response format from Google AI:', result);
    //             return null;
    //         }
    //     } catch (error) {
    //         console.error('Error generating embedding with Google AI:', error);
    //         return null;
    //     }
    // },
    ollama: async ({
        text,
        uri = 'http://127.0.0.1:11434/api/embeddings',
        model = ollamaModel || "mxbai-embed-large:latest",
    }) => {
        if (!text) {
            console.error('ollama: Error no text', text);
            return null
        };

        try {
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model, prompt: text,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Ollama embedding error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding;
        } catch (error) {
            console.error('Error generating embedding with Ollama:', error);
            return null;
        }
    },
    bertTurkish: async ({ text }) => {
        if (!text) {
            console.error('bertTurkish: Error no text', text);
            return null;
        }

        try {
            const response = await fetch('http://127.0.0.1:8080/embed', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                console.error('BERT Turkish Service Error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding; // Float32Array değilse zaten array gelir
        } catch (error) {
            console.error('Error generating Turkish BERT embedding:', error);
            return null;
        }
    }, 
}

function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    // HTML kod bloklarını kaldır
    let cleaned = text; // text.replace(/<[^>]*>/g, ' ');
    // HTML escape karakterlerini karşılıklarıyla değiştir
    cleaned = cleaned
        .replace(/&nbsp;/gi, ' ')
        .replace(/&amp;/gi, '&')
        .replace(/&lt;/gi, '<')
        .replace(/&gt;/gi, '>')
        .replace(/&quot;/gi, '"')
        .replace(/&#39;/gi, "'")
        .replace(/&apos;/gi, "'")
        // Diğer tüm &xxxx; biçimindekileri boşlukla değiştir
        .replace(/&[a-zA-Z0-9#]+;/g, ' ');

    // HTML kod bloklarını kaldır
    cleaned = cleaned.replace(/<[^>]*>/g, ' ');
    // \t, \n, \r ve kontrol karakterlerini temizle (0x00-0x1F ve 0x7F)
    cleaned = cleaned.replace(/[\t\n\r]/g, ' ');
    cleaned = cleaned.replace(/[\u0000-\u001F\u007F]/g, '');
    // Fazla boşlukları tek boşluğa indir
    cleaned = cleaned.replace(/\s+/g, ' ');
    // Önce Türkçe karakterleri eşlenik İngilizce karakterlerle değiştir
    cleaned = cleaned
        .replace(/ç/g, 'c')
        .replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g')
        .replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i')
        .replace(/İ/g, 'I')
        .replace(/ö/g, 'o')
        .replace(/Ö/g, 'O')
        .replace(/ş/g, 's')
        .replace(/Ş/g, 'S')
        .replace(/ü/g, 'u')
        .replace(/Ü/g, 'U');
    // Sonra lowercase dönüşümü
    cleaned = cleaned.toLowerCase();
    return cleaned
    // return text
}
function prepareVectorText(object, fields = []) {
    // Seçili alanları normalize edip birleştir
    const parts = fields
        .filter(field => Object.prototype.hasOwnProperty.call(object, field))
        .map(field => normalizeText(object[field] || ''));
    const combined = parts.join(' ');
    return normalizeText(combined);
}

async function main() {
    const dtBop = Date.now();

    try {
        // Komut satırı argümanlarını al
        const arg = process.argv.slice(2);
        // 1. argüman model adı (opsiyonel)
        let modelType = (arg && arg.length > 0 && typeof arg[0] === 'string') ? arg[0].toLowerCase() : 'ollama';

        // Kullanıcıya seçenekleri göster
        if (arg.length === 0) {
            console.log(`\n[Bilgi] Kullanılabilir embedding modelleri:`);
            console.log(`  - gemini (Google)`);
            console.log(`  - ollama (varsayılan)`);
            console.log(`  - bert`);
            console.log(`[Seçili model]: ${modelType}\n`);
        }
        // Model fonksiyonunu seç
        let embeddingFunc;
        if (modelType === 'gemini') {
            embeddingFunc = vectorEmbeddings.google_v1;
        } else if (modelType === 'bert') {
            embeddingFunc = vectorEmbeddings.bertTurkish;
        } else {
            embeddingFunc = vectorEmbeddings.ollama;
            modelType = 'ollama'; // bilinmeyen değer gelirse de ollama
        }

        const tables2Sync = [
            {
                mongoCollection: 'tourai.data.dim.hotelsdomestic',
                needsVectorEmbedding: true,
                embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            },
        ];

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');

        // Embedding dosyası ayarları
        const embeddingDir = path.resolve(__dirname || path.dirname(new URL(import.meta.url).pathname), './');
        const embeddingFile = path.join(embeddingDir, 'stg4.embeddings.txt');

        // Embedding dosyasını temizle veya oluştur
        try {
            await fs.mkdir(embeddingDir, { recursive: true });
            await fs.writeFile(embeddingFile, '', 'utf8');
            console.log(`📝 Embedding dosyası temizlendi: ${embeddingFile}`);
        } catch (err) {
            console.error('💥 Embedding dosyası oluşturulamadı:', err);
            process.exit(1);
        }

        // Vector Embedding işlemleri
        console.log(`\n🤖 VECTOR EMBEDDING İŞLEMLERİ`);
        console.log(`=====================================`);

        const vectorResults = [];
        const vectorCollections = tables2Sync.filter(table => table.needsVectorEmbedding);
 
        for (let i = 0; i < vectorCollections.length; i++) {
            const tableInfo = vectorCollections[i];
            const vectorStartTime = Date.now();

            console.log(`\n[${i + 1}/${vectorCollections.length}] ${tableInfo.mongoCollection} için vector embedding işlemi başlıyor...`);

            try {
                const collection = mongoDb.collection(tableInfo.mongoCollection);

                // Koleksiyondaki toplam kayıt sayısını al
                const totalRecords = await collection.countDocuments();
                if (totalRecords === 0) {
                    console.log(`  ⚠️  ${tableInfo.mongoCollection} koleksiyonunda kayıt bulunamadı, vector embedding atlanıyor...`);
                    vectorResults.push({
                        collection: tableInfo.mongoCollection,
                        processedCount: 0,
                        duration: Date.now() - vectorStartTime,
                        status: 'skipped'
                    });
                    continue;
                }

                // Kayıtları batch'ler halinde işle
                const batchSize = modeTest ? modeTestLen : 150; // 100’lük batch
                let processedCount = 0;
                let successCount = 0;
                let errorCount = 0;

                console.log(`  📊 ${totalRecords} kayıt bulundu`);
                console.log(`  🔄 Vector embedding'ler oluşturuluyor..., ${batchSize} kadarlık batch'lerde...`);

                // Toplam batch sayısı
                const totalBatches = Math.ceil(totalRecords / batchSize);

                for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    // Her batch için TESIS_ID'leri aggregate ile çek
                    const pipeline = [
                        { $skip: batchIndex * batchSize },
                        { $limit: batchSize },
                        // { $project: { _id: 0 } }
                    ];
                    const batchTime = new Date().toISOString();
                    const batchDocs = await collection.aggregate(pipeline).toArray();
                    if (modeTest && batchIndex == 2) break;
                    // Promise.all ile paralel işle
                    await Promise.all(
                        batchDocs.map(async (doc, subbatchIndex) => {
                            try {
                                const now = new Date().toISOString();
                                try {
                                    const dtProc = Date.now();
                                    const vectorText = prepareVectorText(doc, tableInfo.embeddingFields);
                                    if (!vectorText || vectorText.trim() === '') {
                                        console.error('prepareVectorText hatası:', doc);
                                        processedCount++;
                                        return;
                                    }
                                    const embedding = await embeddingFunc({ text: vectorText, debug: subbatchIndex == 199 });
                                    if (embedding && Array.isArray(embedding)) {
                                        const line = JSON.stringify({
                                            _id: doc._id,
                                            batchIndex,
                                            subbatchIndex,
                                            batchTime,
                                            timestamp: now,
                                            elapsed: Date.now() - dtProc,
                                            TESIS_ID: doc.TESIS_ID,
                                            embedding
                                        }) + '\n';
                                        await fs.appendFile(embeddingFile, line, 'utf8');
                                        successCount++;
                                    } else {
                                        errorCount++;
                                    }
                                    // processedCount++;

                                } catch (err) {
                                    console.error('prepareVectorText hatası:', err);
                                }
                                // successCount++;
                            } catch (err) {
                                errorCount++;
                                console.error('Dosyaya yazma hatası:', err);
                            }
                            processedCount++;
                        })
                    );

                    console.log(`    Batch ${batchIndex + 1}/${totalBatches} işlendi (${processedCount}/${totalRecords})`);
                }

                const vectorElapsedTime = Date.now() - vectorStartTime;
                console.log(`  ✅ Vector embedding tamamlandı. İşlenen: ${processedCount}, Başarılı: ${successCount}, Hatalı: ${errorCount} (${vectorElapsedTime}ms)`);

                vectorResults.push({
                    collection: tableInfo.mongoCollection,
                    processedCount: processedCount,
                    successCount: successCount,
                    errorCount: errorCount,
                    duration: vectorElapsedTime,
                    status: 'completed'
                });

            } catch (vectorError) {
                const vectorElapsedTime = Date.now() - vectorStartTime;
                console.error(`  ❌ ${tableInfo.mongoCollection} vector embedding hatası: ${vectorError.message} (${vectorElapsedTime}ms)`);

                vectorResults.push({
                    collection: tableInfo.mongoCollection,
                    processedCount: 0,
                    successCount: 0,
                    errorCount: 0,
                    duration: vectorElapsedTime,
                    status: 'error',
                    error: vectorError.message
                });
            }
        }

        // Özet raporu
        const totalElapsedTime = Date.now() - dtBop;

        // Vector Embedding özeti
        if (vectorResults.length > 0) {
            console.log(`\n🤖 VECTOR EMBEDDING ÖZETİ`);
            console.log(`=====================================`);

            let totalVectorProcessed = 0;
            let totalVectorSuccess = 0;
            let totalVectorErrors = 0;
            let vectorSuccessCount = 0;
            let vectorErrorCount = 0;
            let vectorSkippedCount = 0;

            vectorResults.forEach((result, index) => {
                const statusIcon = result.status === 'completed' ? '✅' :
                                  result.status === 'error' ? '❌' : '⏭️';

                console.log(`  ${statusIcon} [${index + 1}] ${result.collection}`);
                console.log(`      📊 İşlenen kayıt: ${result.processedCount.toLocaleString()}`);
                console.log(`      ✅ Başarılı embedding: ${result.successCount || 0}`);
                console.log(`      ❌ Hatalı embedding: ${result.errorCount || 0}`);
                console.log(`      ⏱️  Süre: ${result.duration}ms (${(result.duration/1000).toFixed(2)}s)`);

                if (result.error) {
                    console.log(`      ⚠️  Hata: ${result.error}`);
                }

                totalVectorProcessed += result.processedCount || 0;
                totalVectorSuccess += result.successCount || 0;
                totalVectorErrors += result.errorCount || 0;

                if (result.status === 'completed') vectorSuccessCount++;
                else if (result.status === 'error') vectorErrorCount++;
                else vectorSkippedCount++;
            });

            console.log(`\n🤖 VECTOR EMBEDDING İSTATİSTİKLERİ`);
            console.log(`=====================================`);
            console.log(`✅ Başarılı koleksiyon: ${vectorSuccessCount}`);
            console.log(`❌ Hatalı koleksiyon: ${vectorErrorCount}`);
            console.log(`⏭️  Atlanan koleksiyon: ${vectorSkippedCount}`);
            console.log(`📊 Toplam işlenen kayıt: ${totalVectorProcessed.toLocaleString()}`);
            console.log(`✅ Başarılı embedding: ${totalVectorSuccess.toLocaleString()}`);
            console.log(`❌ Hatalı embedding: ${totalVectorErrors.toLocaleString()}`);
            if (totalVectorProcessed > 0) {
                const vectorSuccessRate = ((totalVectorSuccess / totalVectorProcessed) * 100).toFixed(1);
                console.log(`📈 Başarı oranı: ${vectorSuccessRate}%`);
            }

            console.log(`\n⏱️  TOPLAM SÜRE: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
            if (totalVectorSuccess > 0) {
                const avgTimePerEmbedding = (totalElapsedTime / totalVectorSuccess).toFixed(2);
                console.log(`⚡ Ortalama embedding süresi: ${avgTimePerEmbedding}ms/embedding`);
            }
        }

        console.log(`\n🎉 Tüm vector embedding işlemleri tamamlandı!`);

        // Bağlantıyı kapat
        await mongoClient.close();
        // process.exit(1);
        // await mongoClient.close();
    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// __dirname'i ES module ortamında tanımla
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Script'i çalıştır
main();
